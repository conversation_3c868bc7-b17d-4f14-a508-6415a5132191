#!/bin/bash

#===============================================================================
# PostgreSQL 和 TDengine 数据库执行器
#===============================================================================
# 功能描述: 提供数据库连接、SQL执行、版本管理等核心功能
# 支持数据库: PostgreSQL, TDengine
# 创建时间: 2024年
#
# 主要功能:
# - PostgreSQL 数据库连接和操作
# - TDengine 时序数据库连接和操作
# - 数据库版本管理和迁移
# - SQL 文件执行和错误处理
#===============================================================================

# 遇到错误立即退出
set -e

#===============================================================================
# PostgreSQL 数据库操作函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: psqlGetSchema
# 功能: 从PostgreSQL连接字符串中提取schema名称
# 参数:
#   $1: PostgreSQL连接字符串
# 返回: schema名称
# 说明: 通过正则表达式解析连接字符串中的search_path参数
#       URL编码中%3d表示等号(=)
#-------------------------------------------------------------------------------
psqlGetSchema() {
  local psql="$1"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "ERROR: PostgreSQL连接字符串不能为空" >&2
    return 1
  fi

  # 使用正则表达式提取schema名称
  if [[ "$psql" =~ search_path%3d(.*)$ ]]; then
    echo "${BASH_REMATCH[1]}"
  else
    echo "ERROR: 无法从连接字符串中提取schema信息" >&2
    return 1
  fi
}

#-------------------------------------------------------------------------------
# 函数名: psqlCheckConn
# 功能: 检查PostgreSQL数据库连接是否正常
# 参数:
#   $1: PostgreSQL连接字符串
# 返回: 0(连接成功), 非0(连接失败)
# 说明: 通过执行简单的SELECT语句来测试数据库连接
#       使用静默模式避免输出干扰
#-------------------------------------------------------------------------------
psqlCheckConn() {
  local psql="$1"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "ERROR: PostgreSQL连接字符串不能为空" >&2
    return 1
  fi

  # 执行简单查询测试连接
  echo "DEBUG: 测试数据库连接..." >&2

  set +e
  $psql -c "SELECT 1;" 1>/dev/null 2>&1
  local result=$?

  echo "DEBUG: 连接结果码: $result" >&2
  echo "DEBUG: 准备返回到调用函数..." >&2

  # 不要在这里重新启用 set -e，让调用方处理
  return $result
}

#-------------------------------------------------------------------------------
# 函数名: psqlCheckSchema
# 功能: 检查PostgreSQL数据库中指定的schema是否存在
# 参数:
#   $1: PostgreSQL连接字符串
# 返回: 无（schema不存在时直接退出程序）
# 说明: 从连接字符串中提取schema名称，然后查询information_schema验证其存在性
#       使用系统表information_schema.schemata进行验证
#-------------------------------------------------------------------------------
psqlCheckSchema() {
  local psql="$1"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "ERROR: PostgreSQL 连接字符串不能为空" >&2
    exit 1
  fi

  # 提取schema名称
  local schema
  schema=$(psqlGetSchema "$psql")
  if [ $? -ne 0 ] || [ -z "$schema" ]; then
    echo "ERROR: 无法从连接字符串中提取 schema 信息" >&2
    exit 1
  fi

  echo "INFO: 检查数据库schema: $schema"

  # 查询schema是否存在
  local sqlRes
  echo "DEBUG: $psql -c \"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '$schema';\"" >&2
  sqlRes=$($psql -c "SELECT schema_name FROM information_schema.schemata WHERE schema_name = '$schema';" 2>&1)
  local query_result=$?

  # 检查查询结果
  if [ $query_result -ne 0 ]; then
    echo "ERROR: 查询schema时发生错误: $sqlRes" >&2
    exit 1
  elif [[ "$sqlRes" == *"(0 rows)"* ]]; then
    echo "ERROR: 数据库中不存在schema='$schema'，请检查数据库配置" >&2
    exit 1
  else
    echo "INFO: Schema '$schema' 验证通过"
  fi
}

#===============================================================================
# 数据库版本管理函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: psqlGetCurVer
# 功能: 获取PostgreSQL数据库中当前部署的版本号
# 参数:
#   $1: PostgreSQL连接字符串
# 返回: 当前版本号字符串（如果存在）
# 说明: 从databasechangelog表中查询最新的版本标签
#       如果表不存在或无记录则返回空，出错时退出程序
#       版本信息按执行时间倒序排列，取最新的一条记录
#-------------------------------------------------------------------------------
psqlGetCurVer() {
  local psql="$1"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "ERROR: PostgreSQL连接字符串不能为空" >&2
    return 1
  fi

  # 提取schema名称
  local schema
  schema=$(psqlGetSchema "$psql")
  if [ $? -ne 0 ] || [ -z "$schema" ]; then
    echo "ERROR: 无法从连接字符串中提取schema信息" >&2
    return 1
  fi

  # 检查 databasechangelog 表是否存在
  local checkExist
  echo "DEBUG: $psql -c \"SELECT 1 FROM information_schema.tables WHERE table_schema='$schema' AND table_name='databasechangelog';\"" >&2
  checkExist=$($psql -c "SELECT 1 FROM information_schema.tables WHERE table_schema='$schema' AND table_name='databasechangelog';" 2>&1)
  local check_result=$?

  if [ $check_result -ne 0 ]; then
    echo "ERROR: 检查 databasechangelog 表时发生错误: $checkExist" >&2
    exit 1
  elif [[ "$checkExist" == *"(0 rows)"* ]]; then
    echo "DEBUG: databasechangelog 表不存在，可能是全新安装" > /dev/tty
    return 0
  fi

  # 查询当前版本号
  local curVer
  echo "DEBUG: $psql -c \"SELECT tag FROM ${schema}.databasechangelog ORDER BY dateexecuted DESC LIMIT 1;\"" >&2
  curVer=$($psql -c "SELECT tag FROM ${schema}.databasechangelog ORDER BY dateexecuted DESC LIMIT 1;" 2>&1)
  local query_result=$?

  # 分析查询结果
  if [ $query_result -ne 0 ]; then
    echo "ERROR: 查询版本信息时发生错误: $curVer" >&2
    exit 1
  elif [[ "$curVer" == *"(0 rows)"* ]]; then
    echo "DEBUG: databasechangelog 表中没有版本记录" > /dev/tty
    return 0
  elif [[ "$curVer" == *"ERROR"* ]] || [[ "$curVer" == *"error"* ]]; then
    echo "ERROR: 查询版本信息失败: $curVer" >&2
    exit 1
  else
    # 提取版本号（通常在第3行）
    curVer=$(awk -F: 'NR == 3' <<< "$curVer" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
    if [ -n "$curVer" ]; then
      echo "$curVer"
    fi
  fi
}

#-------------------------------------------------------------------------------
# 函数名: psqlCreateCvsTable
# 功能: 创建数据库版本管理相关的表结构
# 参数:
#   $1: PostgreSQL连接字符串
# 返回: 无
# 说明: 创建databasechangelog和databasechangeloglock表
#       用于记录数据库版本变更历史和锁定状态
#       兼容Liquibase数据库版本管理工具的表结构
#       使用IF NOT EXISTS确保表创建的幂等性
#-------------------------------------------------------------------------------
psqlCreateCvsTable() {
  local psql="$1"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "ERROR: PostgreSQL连接字符串不能为空" >&2
    exit 1
  fi

  # 提取schema名称
  local schema
  schema=$(psqlGetSchema "$psql")
  if [ $? -ne 0 ] || [ -z "$schema" ]; then
    echo "ERROR: 无法从连接字符串中提取schema信息" >&2
    exit 1
  fi

  echo "INFO: 创建数据库版本管理表结构 (schema: $schema)"

  # 创建版本管理表
  $psql << EOF
-- 创建数据库变更日志表
CREATE TABLE IF NOT EXISTS $schema."databasechangelog" (
  "id" varchar(255) NOT NULL,
  "author" varchar(255) NOT NULL,
  "filename" varchar(255),
  "dateexecuted" timestamp(6) NOT NULL,
  "orderexecuted" int4 NOT NULL,
  "exectype" varchar(10),
  "md5sum" varchar(35),
  "description" varchar(255),
  "comments" varchar(255),
  "tag" varchar(255),
  "liquibase" varchar(20),
  "contexts" varchar(255),
  "labels" varchar(255),
  "deployment_id" varchar(10)
);

-- 创建数据库变更锁定表
CREATE TABLE IF NOT EXISTS $schema."databasechangeloglock" (
  "id" int4 NOT NULL,
  "locked" bool NOT NULL,
  "lockgranted" timestamp(6),
  "lockedby" varchar(255),
  CONSTRAINT "databasechangeloglock_pkey" PRIMARY KEY ("id")
);

-- 初始化锁定表数据
INSERT INTO $schema."databasechangeloglock" ("id", "locked")
SELECT 1, false
WHERE NOT EXISTS (SELECT 1 FROM $schema."databasechangeloglock" WHERE "id" = 1);
EOF

  local create_result=$?
  if [ $create_result -eq 0 ]; then
    echo "INFO: 数据库版本管理表创建成功"
  else
    echo "ERROR: 数据库版本管理表创建失败" >&2
    exit 1
  fi
}

#-------------------------------------------------------------------------------
# 函数名: psqlMarkVersion
# 功能: 在数据库中标记当前部署的版本号
# 参数:
#   $1: PostgreSQL连接字符串
#   $2: 版本号字符串
# 返回: 无
# 说明: 向databasechangelog表中插入版本记录
#       记录版本号、作者、执行时间等信息，用于版本追踪
#       使用当前时间戳作为执行时间
#-------------------------------------------------------------------------------
psqlMarkVersion() {
  local psql="$1"
  local version="$2"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "ERROR: PostgreSQL连接字符串不能为空" >&2
    exit 1
  fi

  if [ -z "$version" ]; then
    echo "ERROR: 版本号不能为空" >&2
    exit 1
  fi

  # 提取schema名称
  local schema
  schema=$(psqlGetSchema "$psql")
  if [ $? -ne 0 ] || [ -z "$schema" ]; then
    echo "ERROR: 无法从连接字符串中提取schema信息" >&2
    exit 1
  fi

  # 生成当前时间戳
  local current_time
  current_time=$(date '+%Y-%m-%d %H:%M:%S')

  echo "INFO: 标记部署版本: $version"

  # 插入版本记录
  local insert_sql="INSERT INTO $schema.databasechangelog(id, author, dateexecuted, orderexecuted, tag) VALUES('$version', 'compose_deploy', '$current_time', 1, '$version');"

  local result
  echo "DEBUG: $psql -c \"$insert_sql\"" >&2
  result=$($psql -c "$insert_sql" 2>&1)
  local insert_result=$?

  if [ $insert_result -eq 0 ]; then
    echo "INFO: 版本标记成功: $version"
  else
    echo "ERROR: 版本标记失败: $result" >&2
    exit 1
  fi
}

#===============================================================================
# SQL 执行函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: psqlExecSqlFile
# 功能: 执行指定的SQL文件
# 参数:
#   $1: PostgreSQL连接字符串
#   $2: SQL文件路径
# 返回: 无（执行失败时直接退出程序）
# 说明: 读取SQL文件并在数据库中执行，捕获错误信息
#       如果执行过程中出现ERROR则输出错误信息并退出程序
#       只捕获stderr以检测错误，stdout被重定向到/dev/null
#-------------------------------------------------------------------------------
psqlExecSqlFile() {
  local psql="$1"
  local sqlFile="$2"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "ERROR: PostgreSQL连接字符串不能为空" >&2
    exit 1
  fi

  if [ -z "$sqlFile" ]; then
    echo "ERROR: SQL文件路径不能为空" >&2
    exit 1
  fi

  # 检查SQL文件是否存在
  if [ ! -f "$sqlFile" ]; then
    echo "ERROR: SQL文件不存在: $sqlFile" >&2
    exit 1
  fi

  # 检查SQL文件是否可读
  if [ ! -r "$sqlFile" ]; then
    echo "ERROR: SQL文件不可读: $sqlFile" >&2
    exit 1
  fi

  echo "INFO: 正在执行SQL文件: $sqlFile"

  # 执行SQL文件，只捕获错误输出
  local sqlErr
  sqlErr=$($psql < "$sqlFile" 2>&1 1>/dev/null)
  local exec_result=$?

  # 检查执行结果
  if [ $exec_result -ne 0 ] || [[ "$sqlErr" == *"ERROR"* ]]; then
    echo "ERROR: SQL文件执行失败: $sqlFile" >&2
    echo "ERROR: 错误信息: $sqlErr" >&2
    exit 1
  elif [ -n "$sqlErr" ]; then
    # 输出警告信息（如果有的话）
    echo "WARNING: SQL执行警告: $sqlErr" >&2
  fi

  echo "INFO: SQL文件执行成功: $sqlFile"
}

#-------------------------------------------------------------------------------
# 函数名: psqlExecSql
# 功能: 执行指定的SQL语句
# 参数:
#   $1: PostgreSQL连接字符串
#   $2: SQL语句字符串
# 返回: SQL执行结果（包括标准输出和错误输出）
# 说明: 直接执行SQL语句并返回完整的执行结果
#       不进行错误处理，由调用方决定如何处理结果
#       适用于需要获取查询结果的场景
#-------------------------------------------------------------------------------
psqlExecSql() {
  local psql="$1"
  local sql="$2"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "ERROR: PostgreSQL连接字符串不能为空" >&2
    return 1
  fi

  if [ -z "$sql" ]; then
    echo "ERROR: SQL语句不能为空" >&2
    return 1
  fi

  # 执行SQL语句并返回结果
  echo "DEBUG: $psql -c \"$sql\"" >&2
  $psql -c "$sql" 2>&1
}

#===============================================================================
# TDengine 时序数据库操作函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: tdsqlCheckConn
# 功能: 检查TDengine时序数据库连接是否正常
# 参数:
#   $1: TDengine连接字符串
# 返回: 0(连接成功), 非0(连接失败)
# 说明: 通过执行show users命令来测试TDengine数据库连接
#       -s参数表示静默模式执行
#-------------------------------------------------------------------------------
tdsqlCheckConn() {
  local tdsql="$1"

  # 检查参数是否为空
  if [ -z "$tdsql" ]; then
    echo "ERROR: TDengine连接字符串不能为空" >&2
    return 1
  fi

  # 执行show users命令测试连接
  $tdsql -s "SHOW USERS;" 1>/dev/null 2>&1
  return $?
}

#-------------------------------------------------------------------------------
# 函数名: tdsqlImport
# 功能: 导入TDengine初始化SQL脚本
# 参数:
#   $1: TDengine连接字符串
# 返回: 0(导入成功), 非0(导入失败)
# 说明: 执行TDengine容器内的初始化SQL脚本文件
#       脚本路径为容器内的固定路径
#-------------------------------------------------------------------------------
tdsqlImport() {
  local tdsql="$1"
  local init_script="/home/<USER>/init-taosdb.sql"

  # 检查参数是否为空
  if [ -z "$tdsql" ]; then
    echo "ERROR: TDengine连接字符串不能为空" >&2
    return 1
  fi

  echo "INFO: 正在导入TDengine初始化脚本: $init_script"

  # 执行初始化脚本
  $tdsql -s "SOURCE $init_script;" 1>/dev/null 2>&1
  local result=$?

  if [ $result -eq 0 ]; then
    echo "INFO: TDengine初始化脚本导入成功"
  else
    echo "ERROR: TDengine初始化脚本导入失败" >&2
  fi

  return $result
}

#-------------------------------------------------------------------------------
# 函数名: tdsqlCheckInit
# 功能: 检查TDengine时序数据库初始化状态
# 参数: 无
# 返回: 初始化版本信息或退出程序
# 说明: 通过查询log.log表来验证TDengine数据库是否正确初始化
#       如果初始化成功则输出调试信息，失败则退出程序
#       使用固定的数据库连接参数
#-------------------------------------------------------------------------------
tdsqlCheckInit() {
  local container_name="tdengine"
  local db_host="tdengine"
  local db_user="root"
  local db_pass="bdtdtd@123"
  local test_query="SELECT TODAY() FROM log.log;"

  echo "INFO: 检查TDengine数据库初始化状态..."

  # 执行测试查询
  local curVer
  curVer=$(docker exec -i "$container_name" taos -h "$db_host" -u"$db_user" -p"$db_pass" -s "$test_query" 2>&1)
  local query_result=$?

  # 分析查询结果
  if [[ "$curVer" == *"(Query OK)"* ]]; then
    echo "DEBUG: 时序数据库初始化验证成功" > /dev/tty
    return 0
  elif [[ "$curVer" == *"error"* ]] || [ $query_result -ne 0 ]; then
    echo "ERROR: TDengine初始化检查失败，错误信息: $curVer" > /dev/tty
    exit 1
  else
    # 提取版本信息（如果有的话）
    curVer=$(awk -F: 'NR == 3' <<< "$curVer" | sed 's/ //g')
    if [ -n "$curVer" ]; then
      echo "$curVer"
    fi
  fi
}
