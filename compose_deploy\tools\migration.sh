#!/bin/bash

#===============================================================================
# 数据库迁移工具
#===============================================================================
# 功能描述: 提供 Hasura GraphQL 数据库迁移和控制台管理功能
# 支持功能: Hasura 控制台启动、数据库迁移管理
# 创建时间: 2024年
#
# 主要功能:
# - 启动 Hasura 控制台进行数据库迁移管理
# - 提供 GraphQL API 和管理界面
# - 支持数据库架构变更和数据迁移
#
# 函数列表：
# - start_hasura_console
#===============================================================================

# 遇到错误立即退出
set -e

#-------------------------------------------------------------------------------
# 函数名: start_hasura_console
# 功能: 启动 Hasura 控制台进行数据库迁移管理
# 参数:
#   $1: Hasura GraphQL Engine 的 API 端点 URL
# 返回: 无（启动失败时直接退出程序）
# 说明: 启动 Hasura 控制台容器，提供 Web 界面进行数据库迁移管理
#       控制台端口: 9999，API 端口: 10000
#       需要确保 BASE_SERVICE_PATH 环境变量已设置
#
# 使用示例:
#   start_hasura_console "http://localhost:8080"
#-------------------------------------------------------------------------------
start_hasura_console() {
    local endpoint="$1"

    # 检查参数
    if [ -z "$endpoint" ]; then
        echo "ERROR: Hasura GraphQL Engine 端点 URL 不能为空" >&2
        echo "使用方法: start_hasura_console <endpoint_url>" >&2
        exit 1
    fi

    # 检查必要的环境变量
    if [ -z "$BASE_SERVICE_PATH" ]; then
        echo "ERROR: BASE_SERVICE_PATH 环境变量未设置" >&2
        exit 1
    fi

    # 检查 Hasura 配置目录是否存在
    local hasura_config_dir="$BASE_SERVICE_PATH/hasura"
    if [ ! -d "$hasura_config_dir" ]; then
        echo "ERROR: Hasura 配置目录不存在: $hasura_config_dir" >&2
        exit 1
    fi

    echo "INFO: 启动 Hasura 控制台..."
    echo "INFO: 配置目录: $hasura_config_dir"
    echo "INFO: GraphQL 端点: $endpoint"
    echo "INFO: 控制台地址: http://localhost:9999"
    echo "INFO: API 端口: 10000"

    # 启动 Hasura 控制台容器
    docker run --rm -it --name hasura-console \
        -p "9999-10000:9999-10000" \
        --entrypoint="" \
        -v "$BASE_SERVICE_PATH/hasura:/app" \
        --workdir=/app \
        -e HASURA_GRAPHQL_CONSOLE_ASSETS_DIR=/srv/console-assets \
        harbor2.qdbdtd.com:8088/middleware/hasura/graphql-engine:v1.3.2.cli-migrations-v2 \
        hasura-cli console \
        --address 0.0.0.0 \
        --console-port 9999 \
        --api-port 10000 \
        --skip-update-check \
        --endpoint "$endpoint"
}
