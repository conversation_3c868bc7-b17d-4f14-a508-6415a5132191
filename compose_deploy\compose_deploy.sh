#!/bin/bash

#===============================================================================
# 综合信息平台 - 主部署脚本
#===============================================================================
# 功能描述: 综合信息平台的主要部署和管理脚本
# 支持功能: 服务部署、启停管理、版本升级、数据备份等
# 创建时间: 2024年
#
# 使用方法: ./compose_deploy.sh [COMMAND] [OPTIONS]
# 详细帮助: ./compose_deploy.sh --help
#===============================================================================

# 遇到错误立即退出
set -e

#===============================================================================
# 全局变量和配置初始化
#===============================================================================

# 脚本版本
PROGNAM_VERSION=v202507302240

# 离线模式标志，默认为false
OFFLINE_MODE=true

# 脚本名称
PROGNAME=$0
# 获取部署路径（脚本所在目录的绝对路径，兼容macOS和Linux不同环境）
if [[ "$OSTYPE" == "darwin"* ]]; then
  DEPLOY_PATH="$PWD"
else
  DEPLOY_PATH=$(dirname $(readlink -f "$0"))
fi

# 加载全局配置文件
echo
echo "| 当前版本: $PROGNAM_VERSION"
# echo "| 加载配置文件和核心库脚本..."
# echo "| 加载配置文件: $DEPLOY_PATH/config/.config.env"
source "$DEPLOY_PATH/config/.config.env"

# 加载基础服务环境变量
if [ -f "$DEPLOY_PATH/services/base/.env" ]; then
  # echo "| 加载基础服务环境变量: $DEPLOY_PATH/services/base/.env"
  source "$DEPLOY_PATH/services/base/.env"
fi

# 加载所有核心库脚本
echo "| 加载核心库脚本..."
for scripts in $DEPLOY_PATH/libs/*.sh; do
  # echo "|  - 加载: $(basename "$scripts")"
  source "$scripts"
done
echo

#===============================================================================
# 数据库连接配置
#===============================================================================

# PostgreSQL 主数据库连接 (GIS数据库)
# 注意: 5432为容器内端口，非宿主机映射端口
# 从环境变量中读取密码，并进行URL编码处理
POSTGRES_PASSWORD_ENCODED=$(echo "${POSTGRES_PASSWORD}" | sed 's/%/%25/g; s/\^/%5E/g; s/\*/%2A/g; s/&/%26/g; s/#/%23/g; s/+/%2B/g; s/ /%20/g')
psql="docker exec -i base_postgres psql postgres://${POSTGRES_USERNAME}:${POSTGRES_PASSWORD_ENCODED}@${POSTGRES_HOST}:${POSTGRES_PORT}/postgres?options=--search_path%3dpublic"

# PostgreSQL 业务数据库连接 (MES数据库)
# 从环境变量中读取密码，并进行URL编码处理
BLADEX_POSTGRES_PASSWORD_ENCODED=$(echo "${BLADEX_POSTGRES_PASSWORD}" | sed 's/%/%25/g; s/\^/%5E/g; s/\*/%2A/g; s/&/%26/g; s/#/%23/g; s/+/%2B/g; s/ /%20/g')
bizsql="docker exec -i base_business_postgres psql postgres://${BLADEX_POSTGRES_USERNAME}:${BLADEX_POSTGRES_PASSWORD_ENCODED}@${BLADEX_POSTGRES_HOST}:${BLADEX_POSTGRES_PORT}/postgres?options=--search_path%3dpublic"

# TDengine 时序数据库连接
tdsql="docker exec -i tdengine taos -h tdengine"

#===============================================================================
# 模块和路径配置
#===============================================================================

# 基础模块列表 - 用于初始化和升级操作（此变量保留用于兼容性）
baseModules=("base" "iot" "video" "mos" "app" "topo" "workflow" "gis" "mes")
# 数据库变更集目录（已重构为各服务组的data目录，此变量保留用于兼容性）
changesetDir="$baseDir/changeset"

# 基础服务目录
baseDir="$DEPLOY_PATH/services/base"

#===============================================================================
# 部署、升级和版本管理函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: ask_material_version
# 功能: 获取部署材料版本号，支持Git自动检测或手动输入
# 参数: 无
# 返回: 版本号字符串
#-------------------------------------------------------------------------------
ask_material_version() {
  local version

  # 尝试从 Git 获取当前版本标签
  version=$(git describe --tags 2>/dev/null)

  if [ $? -ne 0 ]; then
    # Git获取失败，询问用户是否手动输入版本号
    while true; do
      read -p "无法从Git获取版本标签，是否输入版本号？(选择N将基于当前目录材料部署) [y/N] : " answer
      answer=${answer:-N}
      case $answer in
        y)
          read -p "请输入当前部署材料的版本号: " version
          if [ -z "$version" ]; then
            echo "版本号不能为空，请重新输入"
            continue
          fi
          break
          ;;
        N)
          version="current-$(date +%Y%m%d-%H%M%S)"
          echo "将基于当前目录材料部署，自动生成版本号: $version"
          break
          ;;
        *) ;;
      esac
    done
  else
    # Git获取成功，确认版本号
    while true; do
      read -p "检测到当前部署材料的GIT版本tag为: [$version], 是否正确? (S=跳过版本号，基于当前材料部署) [y/n/S]: " answer
      answer=${answer:-S}
      case $answer in
        y) break ;;
        n)
          read -p "请输入当前部署材料的版本号: " version
          if [ -z "$version" ]; then
            echo "版本号不能为空，请重新输入"
            continue
          fi
          break
          ;;
        S)
          version="current-$(date +%Y%m%d-%H%M%S)"
          echo "跳过版本号输入，自动生成版本号: $version"
          break
          ;;
        *) ;;
      esac
    done
  fi

  # 最终确认版本号
  while true; do
    read -p "要部署的版本号为: [$version], 是否继续? [y/N] " answer
    answer=${answer:-N}
    case $answer in
      y) echo "$version" && return 0 ;;
      N) return 1 ;;
      *) ;;
    esac
  done
}

#-------------------------------------------------------------------------------
# 函数名: deploy
# 功能: 全新环境部署
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
deploy() {
  echo_yellow "开始全新部署..."

  # 获取版本号
  version=$(ask_material_version)
  [ $? -ne 0 ] && exit

  # 系统检查
  check_system && check_config_env && echo_yellow "系统检查通过，开始部署服务..."

  # Docker 环境初始化
  init_docker

  # 启动数据库
  up_postgres

  # 已部署版本号检查，确认是否为全新环境
  local curVer
  curVer=$(psqlGetCurVer "$psql") || {
    echo_error "获取当前版本失败"
    exit 1
  }
  if [ -n "$curVer" ]; then
    echo_error "当前机器中存在版本号为 [$curVer] 的中台服务，无法进行全新安装"
    echo_error "请清理 base_postgres 容器的数据后再安装"
    stop_services
    exit 1
  fi

  # 初始化模块，执行模块数据库初始化
  echo_yellow "初始化系统模块..."
  init_modules

  # 标记版本号
  psqlMarkVersion "$psql" "$version"

  # 如果启用 IOT 服务，初始化时序数据库
  if [ "$DEPLOY_IOT_SERVICE" = true ]; then
    up_taosdb
  fi

  # 启动所有服务
  start_services
  echo_yellow "部署完成！服务已全部启动"
}

#-------------------------------------------------------------------------------
# 函数名: init_modules
# 功能: 初始化所有系统模块
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
init_modules() {
  echo_yellow "开始初始化系统模块..."

  # 检查数据库架构并创建变更日志表
  psqlCheckSchema "$psql"
  psqlCreateCvsTable "$psql"

  #---------------------------------------------------------------------------
  # GIS模块初始化
  #---------------------------------------------------------------------------
  echo "初始化GIS模块..."

  # Nacos配置中心初始化
  local sqlFile="$DEPLOY_PATH/services/mos/data/init/init-nacos.sql"
  [ -e "$sqlFile" ] && psqlExecSqlFile "$psql" "$sqlFile"

  # 用户认证初始化
  sqlFile="$DEPLOY_PATH/services/mos/data/init/init-auth.sql"
  [ -e "$sqlFile" ] && psqlExecSqlFile "$psql" "$sqlFile"

  # BladeX基础平台初始化
  sqlFile="$DEPLOY_PATH/services/mos/data/init/init-bladex.sql"
  [ -e "$sqlFile" ] && psqlExecSqlFile "$bizsql" "$sqlFile"

  # IOT服务相关初始化
  if [ "$DEPLOY_IOT_SERVICE" = true ]; then
    sqlFile="$DEPLOY_PATH/services/iot/data/init/init-iot.sql"
    [ -e "$sqlFile" ] && psqlExecSqlFile "$psql" "$sqlFile"

    sqlFile="$DEPLOY_PATH/services/iot/data/init/init-push.sql"
    [ -e "$sqlFile" ] && psqlExecSqlFile "$psql" "$sqlFile"
  fi

  # 视频服务初始化
  if [ "$DEPLOY_VIDEO_SERVICE" = true ]; then
    sqlFile="$DEPLOY_PATH/services/gis/data/init/init-video.sql"
    [ -e "$sqlFile" ] && psqlExecSqlFile "$psql" "$sqlFile"
  fi

  # GIS业务服务初始化
  if [ "$DEPLOY_GIS_SERVICE" = true ]; then
    sqlFile="$DEPLOY_PATH/services/gis/data/init/init-gis.sql"
    [ -e "$sqlFile" ] && psqlExecSqlFile "$psql" "$sqlFile"
  fi

  # MES业务服务初始化
  if [ "$DEPLOY_MES_SERVICE" = true ]; then
    sqlFile="$DEPLOY_PATH/services/mes/data/init/init-mes.sql"
    [ -e "$sqlFile" ] && psqlExecSqlFile "$bizsql" "$sqlFile"
  fi

  # 工作流服务初始化
  if [ "$DEPLOY_FLOW_SERVICE" = true ]; then
    sqlFile="$DEPLOY_PATH/services/mes/data/init/init-workflow.sql"
    [ -e "$sqlFile" ] && psqlExecSqlFile "$bizsql" "$sqlFile"
  fi

  #---------------------------------------------------------------------------
  # 执行各模块初始化脚本
  #---------------------------------------------------------------------------
  echo "执行模块初始化脚本..."

  # 执行基础设施初始化脚本
  for script in minio topo; do
    initSh="$DEPLOY_PATH/scripts/init.$script.sh"
    if [ -e "$initSh" ]; then
      curPwd=$(pwd)
      echo "INFO: 执行基础设施初始化脚本 $initSh"
      source "$initSh"
      cd "$curPwd"
    fi
  done

  echo_yellow "系统模块初始化完成！"
}

#-------------------------------------------------------------------------------
# 函数名: update_modules
# 功能: 执行模块版本升级
# 参数: $1 - 当前版本号
#       $2 - 目标版本号
# 返回: 无
#-------------------------------------------------------------------------------
update_modules() {
  local curVer=$1
  local targetVer=$2

  echo_yellow "开始模块升级: $curVer -> $targetVer"

  # 定义服务组和对应的数据库连接
  local service_groups=("base:psql" "gis:psql" "iot:psql" "iot:tdsql" "video:psql" "mes:bizsql" "workflow:bizsql")

  # 遍历各个服务组的迁移脚本
  for service_info in "${service_groups[@]}"; do
    local service_name="${service_info%:*}"
    local db_type="${service_info#*:}"
    local service_path="$DEPLOY_PATH/services/$service_name"

    # 检查服务组是否存在
    if [ ! -d "$service_path/data/migrations" ]; then
      continue
    fi

    # 获取该服务组的所有版本目录并排序
    local versions=$(find "$service_path/data/migrations" -maxdepth 1 -mindepth 1 -type d | xargs -n1 basename | sort -V)

    # 遍历版本目录，执行升级脚本
    for v in $versions; do
      # 检查版本是否在升级范围内
      if [[ $(compare_version "$v" "$curVer") -eq 1 ]] && [[ $(compare_version "$v" "$targetVer") -le 0 ]]; then
        echo "执行 $service_name 服务组版本 $v 的升级脚本..."

        # 根据数据库类型选择连接
        local db_conn
        case $db_type in
          "psql") db_conn="$psql" ;;
          "bizsql") db_conn="$bizsql" ;;
          "tdsql") db_conn="$tdsql" ;;
          *) continue ;;
        esac

        # 执行SQL升级脚本
        local sqlFile="$service_path/data/migrations/$v/up.sql"
        [ -e "$sqlFile" ] && psqlExecSqlFile "$db_conn" "$sqlFile"

        # 执行Shell升级脚本
        local upSh="$service_path/data/migrations/$v/up.sh"
        if [ -e "$upSh" ]; then
          curPwd=$(pwd)
          echo "INFO: 执行升级脚本: $upSh"
          source "$upSh"
          cd "$curPwd"
        fi
      fi
    done
  done

  echo_yellow "模块升级完成！"
}

#-------------------------------------------------------------------------------
# 函数名: update
# 功能: 系统版本升级
# 参数: $1 - 是否滚动升级（可选）
# 返回: 无
#-------------------------------------------------------------------------------
update() {
  local rolling=$1

  echo_yellow "开始系统版本升级..."

  # 系统检查
  check_system && check_config_env

  # 获取目标版本号
  local version
  version=$(ask_material_version)
  [ $? -ne 0 ] && exit

  # 确认服务运行状态
  while true; do
    read -p "中台服务是否正在运行? [Y/N] " reply
    case $reply in
      Y) break;;
      N)
        echo_error "请启动服务后再进行升级"
        exit 1
        ;;
      *) ;;
    esac
  done

  # 获取当前版本号
  local curVer
  curVer=$(psqlGetCurVer "$psql")
  if [ -z "$curVer" ]; then
    echo_error "从数据库 databasechangelog 表中查不到当前部署的版本号，无法升级"
    exit 1
  elif [[ $(compare_version "$curVer" "$version") -ge 0 ]]; then
    echo_error "当前应用版本号为 [$curVer]，无需升级"
    exit 1
  else
    # 确认升级操作
    while true; do
      read -p "要将当前服务 [$curVer] 升级至 [$version]? [Y/N] " reply
      case $reply in
        Y)
          # 执行升级
          update_modules "$curVer" "$version"
          psqlMarkVersion "$psql" "$version"

          # 根据升级模式决定是否重启服务
          if [ -z "$rolling" ]; then
            echo_yellow "停止服务进行升级..."
            stop_services
          else
            echo_yellow "执行滚动升级（不停机）..."
          fi

          start_services
          echo_yellow "升级成功！当前版本: $version"
          exit 0
          ;;
        N)
          echo "升级操作已取消"
          exit 0
          ;;
        *) ;;
      esac
    done
  fi
}

#===============================================================================
# 服务管理函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: up_services
# 功能: 启动所有已配置的服务 `docker compose up -d`
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
up_services() {
  echo_yellow "正在上线服务..."

  # 启动基础服务（必须）
  echo_yellow "上线基础服务..."
  up_base_service

  if [ "$DEPLOY_MOS_SERVICE" = true ]; then
    echo_yellow "上线MOS服务..."
    up_mos_service
  fi

  if [ "$DEPLOY_IOT_SERVICE" = true ]; then
    echo_yellow "上线IOT服务..."
    up_iot_service
  fi

  # 根据配置启动各业务服务
  if [ "$DEPLOY_VIDEO_SERVICE" = true ]; then
    echo_yellow "上线视频服务..."
    up_video_service
  fi

  if [ "$DEPLOY_GIS_SERVICE" = true ]; then
    echo_yellow "上线GIS服务..."
    up_gis_service
  fi

  if [ "$DEPLOY_MES_SERVICE" = true ]; then
    echo_yellow "上线MES服务..."
    up_mes_service
  fi

  if [ "$DEPLOY_APP_SERVICE" = true ]; then
    echo_yellow "上线APP服务..."
    up_app_service
  fi

  if [ "$DEPLOY_FLOW_SERVICE" = true ]; then
    echo_yellow "上线工作流服务..."
    up_workflow_service
  fi

  # SSH服务默认不启动，需要手动上线
  # up_ssh_service

  cd "$DEPLOY_PATH"
  echo_yellow "所有服务上线完成！"
}

#-------------------------------------------------------------------------------
# 函数名: down_services
# 功能: 停止并删除所有服务容器（会造成数据丢失） `docker compose down`
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
down_services() {
  echo_yellow "停止并删除全部服务，会造成部分数据丢失，谨慎操作！"
  echo_yellow "请输入 'yes' 确认继续："
  read
  if [[ "$REPLY" != yes ]]; then
    echo "操作已取消"
    exit 1
  fi

  # 删除各业务服务
  echo_yellow "开始删除服务容器..."
  if [ "$DEPLOY_MOS_SERVICE" = true ]; then down_mos_service; fi
  if [ "$DEPLOY_IOT_SERVICE" = true ]; then down_iot_service; fi
  if [ "$DEPLOY_VIDEO_SERVICE" = true ]; then down_video_service; fi
  if [ "$DEPLOY_MES_SERVICE" = true ]; then down_mes_service; fi
  if [ "$DEPLOY_GIS_SERVICE" = true ]; then down_gis_service; fi
  if [ "$DEPLOY_FLOW_SERVICE" = true ]; then down_workflow_service; fi
  if [ "$DEPLOY_APP_SERVICE" = true ]; then down_app_service; fi

  # 删除基础服务
  echo_yellow "删除基础服务..."
  down_base_service

  cd "$DEPLOY_PATH"
  echo_yellow "所有服务容器已删除！"
}

#-------------------------------------------------------------------------------
# 函数名: start_services
# 功能: 启动所有已配置的服务 `docker compose start`
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
start_services() {
  echo_yellow "正在启动服务..."

  # 启动基础服务（必须）
  echo_yellow "启动基础服务..."
  start_base_service

  if [ "$DEPLOY_MOS_SERVICE" = true ]; then start_mos_service; fi
  if [ "$DEPLOY_IOT_SERVICE" = true ]; then start_iot_service; fi
  if [ "$DEPLOY_VIDEO_SERVICE" = true ]; then start_video_service; fi
  if [ "$DEPLOY_GIS_SERVICE" = true ]; then start_gis_service; fi
  if [ "$DEPLOY_MES_SERVICE" = true ]; then start_mes_service; fi
  if [ "$DEPLOY_FLOW_SERVICE" = true ]; then start_workflow_service; fi
  if [ "$DEPLOY_APP_SERVICE" = true ]; then start_app_service; fi

  # SSH服务默认不启动，需要手动启动
  # start_ssh_service

  cd "$DEPLOY_PATH"
  echo_yellow "所有服务启动完成！"
}

#-------------------------------------------------------------------------------
# 函数名: stop_services
# 功能: 停止所有运行中的服务 `docker compose stop`
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
stop_services() {
  echo_yellow "正在停止全部服务..."

  # 停止各业务服务
  if [ "$DEPLOY_MOS_SERVICE" = true ]; then stop_mos_service; fi
  if [ "$DEPLOY_IOT_SERVICE" = true ]; then stop_iot_service; fi
  if [ "$DEPLOY_VIDEO_SERVICE" = true ]; then stop_video_service; fi
  if [ "$DEPLOY_MES_SERVICE" = true ]; then stop_mes_service; fi
  if [ "$DEPLOY_GIS_SERVICE" = true ]; then stop_gis_service; fi
  if [ "$DEPLOY_FLOW_SERVICE" = true ]; then stop_workflow_service; fi
  if [ "$DEPLOY_APP_SERVICE" = true ]; then stop_app_service; fi

  # 最后停止基础服务
  echo_yellow "停止基础服务..."
  stop_base_service

  # SSH服务默认不停止
  # stop_ssh_service

  cd "$DEPLOY_PATH"
  echo_yellow "所有服务已停止！"
}

#-------------------------------------------------------------------------------
# 函数名: restart_services
# 功能: 重启所有服务 `docker compose restart`
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
restart_services() {
  echo_yellow "即将重启所有服务，是否继续？"
  echo_yellow "请输入 'yes' 确认继续："
  read
  if [[ "$REPLY" != yes ]]; then
    echo "操作已取消"
    exit 1
  fi

  # 重启基础服务
  echo_yellow "开始重启服务..."
  restart_base_service

  # 重启各业务服务
  if [ "$DEPLOY_MOS_SERVICE" = true ]; then restart_mos_service; fi
  if [ "$DEPLOY_IOT_SERVICE" = true ]; then restart_iot_service; fi
  if [ "$DEPLOY_VIDEO_SERVICE" = true ]; then restart_video_service; fi
  if [ "$DEPLOY_MES_SERVICE" = true ]; then restart_mes_service; fi
  if [ "$DEPLOY_GIS_SERVICE" = true ]; then restart_gis_service; fi
  if [ "$DEPLOY_FLOW_SERVICE" = true ]; then restart_workflow_service; fi
  if [ "$DEPLOY_APP_SERVICE" = true ]; then restart_app_service; fi

  cd "$DEPLOY_PATH"
  echo_yellow "所有服务重启完成！"
}

#-------------------------------------------------------------------------------
# 函数名: prune
# 功能: 彻底清理所有数据（不可恢复）
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
prune() {
  echo_yellow "警告：此操作将删除所有数据，不可恢复！"
  echo_yellow "请输入 'yes' 确认删除所有数据："
  read
  if [[ "$REPLY" == yes ]]; then
    down_services && prune_docker
    echo_yellow "所有数据已清理完成！"
  else
    echo "操作已取消"
  fi
}

#===============================================================================
# 数据库关联管理函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: up_postgres
# 功能: 启动PostgreSQL数据库服务
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
up_postgres() {
  echo_yellow "启动 PostgreSQL 数据库..."
  cd "$baseDir"

  # 启动主数据库
  docker_compose_cmd up -d postgres
  wait_postgres

  # 启动业务数据库
  docker_compose_cmd up -d business-postgres
  wait_biz_postgres

  echo_yellow "PostgreSQL 数据库启动完成！"
}

#-------------------------------------------------------------------------------
# 函数名: wait_postgres
# 功能: 等待PostgreSQL主数据库启动完成
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
wait_postgres() {
  local timeout=180
  echo "等待 PostgreSQL 主数据库启动..."

  while true; do
    if [ $timeout -lt 0 ]; then
      echo_error "无法访问 postgres 容器数据库，请检查容器日志: docker logs base_postgres"
      exit 1
    fi

    echo "INFO: 等待 postgres 数据库启动... (剩余${timeout}秒)"

    # 临时禁用 set -e 来调用 psqlCheckConn
    echo "DEBUG: 准备调用 psqlCheckConn..." >&2
    set +e
    psqlCheckConn "$psql"
    local conn_result=$?
    echo "DEBUG: psqlCheckConn 返回，结果码: $conn_result" >&2
    set -e

    echo "INFO: 连接检查完成，结果码: $conn_result"
    if [ $conn_result -eq 0 ]; then
      echo "INFO: 数据库连接成功！"
      break
    else
      echo "INFO: 数据库连接失败(码:$conn_result)，继续等待..."
    fi

    sleep 5
    ((timeout-=5))
  done

  echo_yellow "PostgreSQL 主数据库启动成功！"
}

#-------------------------------------------------------------------------------
# 函数名: wait_biz_postgres
# 功能: 等待PostgreSQL业务数据库启动完成
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
wait_biz_postgres() {
  local timeout=120
  echo "等待 PostgreSQL 业务数据库启动..."

  while true; do
    if [ $timeout -lt 0 ]; then
      echo_error "无法访问 business_postgres 容器数据库，启动超时"
      exit 1
    fi

    echo "INFO: 等待 business_postgres 数据库启动... (剩余${timeout}秒)"
    echo "$bizsql -c \"select 1;\""

    # 临时禁用 set -e 来调用 psqlCheckConn
    set +e
    psqlCheckConn "$bizsql"
    local biz_result=$?
    set -e

    [ $biz_result -eq 0 ] && break

    sleep 3
    ((timeout-=3))
  done

  echo_yellow "PostgreSQL 业务数据库启动成功！"
}

#-------------------------------------------------------------------------------
# 函数名: up_taosdb
# 功能: 初始化时序数据库TDengine
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
up_taosdb() {
  echo_yellow "启动 TDengine 时序数据库..."
  cd "$baseDir"

  # 启动TDengine容器
  docker_compose_cmd up -d tdengine
  wait_taosdb

  # 导入初始化数据
  tdsqlImport "$tdsql"
  tdsqlCheckInit

  echo_yellow "TDengine 时序数据库初始化完成！"
}

#-------------------------------------------------------------------------------
# 函数名: wait_taosdb
# 功能: 等待TDengine时序数据库启动完成
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
wait_taosdb() {
  local timeout=120
  echo "等待 TDengine 时序数据库启动..."

  while true; do
    if [ $timeout -lt 0 ]; then
      echo_error "无法访问 TDengine 容器数据库，启动超时"
      exit 1
    fi

    echo "INFO: 等待时序数据库启动... (剩余${timeout}秒)"
    tdsqlCheckConn "$tdsql"
    [ $? -eq 0 ] && break

    sleep 3
    ((timeout-=3))
  done

  echo_yellow "TDengine 时序数据库启动成功！"
}

#===============================================================================
# 数据备份和恢复函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: create_base_volumes
# 功能: 创建基础Docker数据卷
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
create_base_volumes() {
  echo_yellow "创建基础Docker数据卷..."

  BASE_VOLUMES=(
    "base-rabbitmq"
    "base-red-node"
    "base-redis"
    "base-postgres"
    "base-business-postgres"
    "base-opengauss"
    "base-opengauss-backup"
    "base-opengauss-backup-log"
    "base-business-opengauss"
    "base-business-opengauss-backup"
    "base-business-opengauss-backup-log"
    "bladex-minio"
    "base-sftpgo-data"
    "base-sftpgo-home"
    "data-monitor-image"
    "data-monitor-log"
    "data-storage-log"
    "data-warn-log"
    "converter-monitor-log"
    "gis-biz-log"
    "mes-biz-log"
    "base-workflow"
    "video-redis"
  )

  for volume_name in "${BASE_VOLUMES[@]}"; do
    echo_yellow "创建数据卷: $volume_name"
    docker volume create "$volume_name"
  done

  echo_yellow "基础数据卷创建完成！"
}

#-------------------------------------------------------------------------------
# 函数名: backup
# 功能: 执行数据备份
# 参数: $2 - 备份选项（可选）
# 返回: 无
#-------------------------------------------------------------------------------
backup() {
  local OPTION=$2
  local BACKUP_PATH="$BACKUP_BASE_PATH/$(date "+%Y-%m-%d")"

  echo_yellow "开始数据备份..."
  echo "备份路径: $BACKUP_PATH"

  # 备份Docker命名卷
  echo_yellow "备份Docker数据卷..."
  for volume_name in "${DOCKER_BACKUP_VOLUMES[@]}"; do
    backup_docker_volume "$volume_name"
  done

  # 备份部署材料
  echo_yellow "备份部署材料..."
  tar --exclude='.git' -zcvf "$BACKUP_PATH/deploy_dir.tgz" "$DEPLOY_PATH"

  # 备份 PostgreSQL 数据库
  echo_yellow "备份 PostgreSQL 数据库..."
  backup_postgres

  # TODO: 备份 InfluxDB 数据库
  # TODO: 备份 TDengine 数据库

  # 自动清理旧备份文件（保留3天）
  if [[ "$OPTION" == autorm ]]; then
    echo_yellow "清理3天前的备份文件..."
    find "$BACKUP_PATH" -type d -mtime +2 -exec rm -rf {} \;
  fi

  echo_yellow "数据备份完成！"
}

#-------------------------------------------------------------------------------
# 函数名: enable_auto_backup
# 功能: 设置自动备份任务
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
enable_auto_backup() {
  echo_yellow "配置自动备份任务..."

  # 检查是否已存在自动备份任务
  grep "compose_deploy.sh backup" /etc/crontab
  if [ $? -ne 0 ]; then
    # 添加自动备份任务（每日凌晨3点执行）
    cat >>/etc/crontab <<EOF
00 03 * * * root bash -c "$DEPLOY_PATH/compose_deploy.sh backup autorm" > /dev/null 2>&1
EOF
    echo "开启自动备份，备份目录: $BACKUP_BASE_PATH"
  else
    echo_yellow "备份已开启！"
  fi
}

#===============================================================================
# 版本查询函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: show_version
# 功能: 显示当前部署的版本号
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
show_version() {
  echo_yellow "查询当前部署版本..."
  local version=$(psqlGetCurVer "$psql")
  if [ -z "$version" ]; then
    echo_error "无法获取当前版本信息，请确保服务正在运行"
  else
    echo_yellow "当前部署版本: $version"
  fi
}

#===============================================================================
# 子服务管理函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: ssh_service
# 功能: SSH服务管理
# 参数: $2 - 子命令
# 返回: 无
#-------------------------------------------------------------------------------
ssh_service() {
  local command=$2
  case $command in
    "up")
      echo_yellow "上线 SSH 服务..."
      up_ssh_service
      ;;
    "down")
      echo_yellow "下线 SSH 服务..."
      down_ssh_service
      ;;
    "start")
      echo_yellow "启动 SSH 服务..."
      start_ssh_service
      ;;
    "stop")
      echo_yellow "停止 SSH 服务..."
      stop_ssh_service
      ;;
    *)
      echo_error "错误的 SSH 子命令: $command"
      usage
      ;;
  esac
  cd "$DEPLOY_PATH"
}

#-------------------------------------------------------------------------------
# 函数名: nginx_service
# 功能: Nginx服务管理
# 参数: $2 - 子命令
# 返回: 无
#-------------------------------------------------------------------------------
nginx_service() {
  local command=$2
  case $command in
    "restart")
      restart_nginx
      ;;
    *)
      echo_error "错误的Nginx子命令: $command"
      usage
      ;;
  esac
  cd "$DEPLOY_PATH"
}

#-------------------------------------------------------------------------------
# 函数名: restart_nginx
# 功能: 重启前端 Nginx 网关服务
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
restart_nginx() {
  echo_yellow "重启前端 Nginx 网关..."
  cd "$MOS_SERVICE_PATH"
  docker_compose_cmd stop frontend-nginx-gateway &&
  docker_compose_cmd rm -f frontend-nginx-gateway &&
  docker_compose_cmd up -d frontend-nginx-gateway &&
  cd "$DEPLOY_PATH"
  echo_yellow "Nginx 网关重建完成！"
}

#-------------------------------------------------------------------------------
# 函数名: init_topo
# 功能: 初始化Topo拓扑图模块
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
init_topo() {
  echo_yellow "初始化Topo拓扑图模块..."
  local initSh="$DEPLOY_PATH/scripts/init.topo.sh"
  if [ -e "$initSh" ]; then
    echo "INFO: 执行Topo初始化脚本 $initSh"
    source "$initSh"
    echo_yellow "Topo模块初始化完成！"
  else
    echo_yellow "未找到Topo初始化脚本，跳过初始化"
  fi
}

#-------------------------------------------------------------------------------
# 函数名: topo_service
# 功能: Topo拓扑图服务管理
# 参数: $2 - 子命令
# 返回: 无
#-------------------------------------------------------------------------------
topo_service() {
  local command=$2
  case $command in
    "init")
      init_topo
      ;;
    *)
      echo_error "错误的Topo子命令: $command"
      usage
      ;;
  esac
  cd "$DEPLOY_PATH"
}

#-------------------------------------------------------------------------------
# 函数名: config_service
# 功能: 配置相关服务管理
# 参数: $2 - 子命令
# 返回: 无
#-------------------------------------------------------------------------------
config_service() {
  local command=$2
  case $command in
    "check")
      echo_yellow "检查配置环境..."
      check_config_env
      ;;
    *)
      echo_error "错误的配置子命令: $command"
      usage
      ;;
  esac
  cd "$DEPLOY_PATH"
}

#-------------------------------------------------------------------------------
# 函数名: docker_image_pull
# 功能: 并行拉取所有服务的Docker镜像
# 参数: 无
# 返回: 无
#-------------------------------------------------------------------------------
docker_image_pull() {
  # 离线模式下跳过镜像拉取
  if [ "$OFFLINE_MODE" = true ]; then
    echo_yellow "离线模式：跳过Docker镜像拉取"
    return 0
  fi

  echo_yellow "开始拉取Docker镜像..."

  # 基础服务镜像拉取
  cd "$BASE_SERVICE_PATH" && docker_compose_pull_with_retry &
  # SSH服务镜像拉取
  cd "$SSH_SERVICE_PATH" && docker_compose_pull_with_retry &
  # MOS服务镜像拉取（必须）
  cd "$MOS_SERVICE_PATH" && docker_compose_pull_with_retry &

  # 根据配置拉取各业务服务镜像
  if [ "$DEPLOY_VIDEO_SERVICE" = true ]; then
    cd "$VIDEO_SERVICE_PATH" && docker_compose_pull_with_retry &
  fi
  if [ "$DEPLOY_IOT_SERVICE" = true ]; then
    cd "$IOT_SERVICE_PATH" && docker_compose_pull_with_retry &
  fi
  if [ "$DEPLOY_MES_SERVICE" = true ]; then
    cd "$MES_SERVICE_PATH" && docker_compose_pull_with_retry &
  fi
  if [ "$DEPLOY_GIS_SERVICE" = true ]; then
    cd "$GIS_SERVICE_PATH" && docker_compose_pull_with_retry &
  fi
  if [ "$DEPLOY_FLOW_SERVICE" = true ]; then
    cd "$WORK_FLOW_PATH" && docker_compose_pull_with_retry &
  fi
  if [ "$DEPLOY_APP_SERVICE" = true ]; then
    cd "$APP_SERVICE_PATH" && docker_compose_pull_with_retry &
  fi

  # 拉取工具镜像
  docker pull "$DOCKER_BUSY_BOX_IMAGE"

  cd "$DEPLOY_PATH"
  echo_yellow "Docker镜像拉取完成！"
}

#-------------------------------------------------------------------------------
# 函数名: docker_service
# 功能: Docker相关服务管理
# 参数: $2 - 子命令
# 返回: 无
#-------------------------------------------------------------------------------
docker_service() {
  local command=$2
  case $command in
    "init")
      echo_yellow "初始化Docker环境..."
      init_docker
      ;;
    "pull")
      docker_image_pull
      ;;
    "rmvol")
      shift 2  # 移除 "docker" 和 "rmvol" 参数
      remove_docker_volumes_safely "$@"
      ;;
    *)
      echo_error "错误的Docker子命令: $command"
      usage
      ;;
  esac
  cd "$DEPLOY_PATH"
}

#===============================================================================
# 帮助信息函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: usage
# 功能: 显示脚本使用帮助信息
# 参数: 无
# 返回: 退出码1
#-------------------------------------------------------------------------------
usage() {
  cat <<EOF >&2
===============================================================================
综合信息平台 - 部署脚本使用说明
===============================================================================

使用方法:
  $PROGNAME COMMAND [OPTIONS]

主要命令:
  deploy -i                     在全新环境上部署并启动服务
  up                            上线所有服务
  down                          停止并删除所有服务容器（危险操作）
  start                         启动所有已配置的服务
  stop                          停止所有运行中的服务
  restart                       重启所有服务
  backup                        手动备份数据
  enable-auto-backup            开启自动备份（每日凌晨3点）
  update [-r] [-g -t <tag>]     版本升级
  prune                         彻底清理所有数据（不可恢复）
  version                       查看当前已部署的版本

子服务管理:
  config      [COMMAND]         环境变量配置管理（check）
  ssh         [COMMAND]         SSH 服务管理（start， stop）
  topo        [COMMAND]         Topo 拓扑图服务管理（init）
  nginx       [COMMAND]         Nginx 服务管理（restart）
  docker      [COMMAND]         Docker 服务管理（init，pull，rmvol）

选项说明:
  全局选项:
    -o                          离线模式，不进行网络操作

  update 命令选项:
    -g -t <tag>               更新到指定tag版本的部署材料
    -r                        滚动升级（不停机升级）

  config 子命令:
    check                     检查环境变量配置

  ssh 子命令:
    start                     启动 SSH 调试服务
    stop                      停止 SSH 调试服务

  nginx 子命令:
    restart                   重启前端 Nginx 服务

  topo 子命令:
    init                      初始化 Topo 拓扑图数据

  docker 子命令:
    init                      初始化 Docker 网络和命名卷
    pull                      拉取所有服务的 Docker 镜像
    rmvol <volume1> [volume2] 安全删除指定的Docker数据卷（支持通配符）

使用示例:
  # 全新部署（离线模式）
  $PROGNAME -o deploy -i

  # 启动服务
  $PROGNAME start

  # 滚动升级到指定版本
  $PROGNAME update -r -g -t v1.2.11

  # 检查配置
  $PROGNAME config check

  # 启动SSH调试服务
  $PROGNAME ssh start

  # 删除指定的Docker数据卷
  $PROGNAME docker rmvol volume1 volume2

  # 使用通配符删除匹配的数据卷
  $PROGNAME docker rmvol base-gauss*

注意事项:
  - deploy 命令仅用于全新环境部署
  - down 和 prune 命令会删除数据，请谨慎使用
  - 升级前建议先执行 backup 命令备份数据
  - 各服务的启用状态由 .config.env 文件控制
  - 离线模式(-o)下不会尝试连接网络或拉取镜像

===============================================================================
EOF
  exit 1
}

#===============================================================================
# 主程序入口
#===============================================================================

# 获取主命令
command=$1

# 解析命令行选项
while getopts ":o" opt; do
  case $opt in
    o)
      OFFLINE_MODE=true
      ;;
    \?)
      echo "无效选项: -$OPTARG" >&2
      ;;
  esac
done

# 移除已解析的选项参数
shift $((OPTIND-1))

# 根据命令执行相应操作
case $command in
  "deploy")
    if [ -z "$2" ]; then
      echo_error "deploy 命令需要 -i 参数"
      echo_error "请使用: $PROGNAME deploy -i"
      exit 1
    elif [ "$2" == "-i" ]; then
      deploy
    else
      echo_error "deploy 命令仅支持 -i 参数"
      usage
    fi
    ;;
  "update")
    shift
    # 解析更新命令选项
    while getopts :t:rg o; do
      case $o in
        t) tag=$OPTARG ;;
        r) rolling=true ;;
        g) git_only=true ;;
        *) usage ;;
      esac
    done

    # 处理环境变量覆盖
    if [ ! -z "$SPUG_RELEASE" ]; then
      tag=$SPUG_RELEASE
    fi

    # 执行相应的更新操作
    if [ -n "$git_only" ]; then
      git_update "$tag"
      exit 0
    else
      update "$rolling"
    fi
    ;;
  "backup")
    backup "$@"
    ;;
  "enable-auto-backup")
    enable_auto_backup
    ;;
  "up")
    up_services
    ;;
  "down")
    down_services
    ;;
  "start")
    start_services
    ;;
  "stop")
    stop_services
    ;;
  "restart")
    restart_services
    ;;
  "prune")
    prune
    ;;
  "ssh")
    ssh_service "$@"
    ;;
  "nginx")
    nginx_service "$@"
    ;;
  "topo")
    topo_service "$@"
    ;;
  "config")
    config_service "$@"
    ;;
  "docker")
    docker_service "$@"
    ;;
  "version")
    show_version
    ;;
  "--help"|"help")
    usage
    ;;
  "")
    echo_error "请指定命令"
    usage
    ;;
  *)
    echo_error "未知命令: $command"
    usage
    ;;
esac

# 脚本执行完成
echo_yellow "操作完成！"
